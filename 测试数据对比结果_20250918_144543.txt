测试数据对比结果报告
生成时间: 2025-09-18 14:45:43
处理文件夹数量: 7
文件夹列表: Fr_camera, LK1A_CDC, LK2A_CCM, LK2A_TCU, PK1B_ADCU, PK1B_L2, PK1B_L4
================================================================================


处理文件夹: Fr_camera
Word文件: 网络安全确认测试报告_【LK2A】_【Fr_camera】_【20250829】.docx
Excel文件: LK2A_Fr_camera测试用例.xlsx
测试报告汇总数据量: 48
测试过程数据量: 49
测试用例数据量: 48

=== 处理文件夹: Fr_camera ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN5
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN5
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN_002
  测试报告汇总名称: CAN总线重放测试-CAN5
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN_002
  测试报告汇总名称: CAN总线重放测试-CAN5
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN_005
  测试报告汇总名称: XCP协议扫描测试-CAN5
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN_005
  测试报告汇总名称: XCP协议扫描测试-CAN5
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN_001
  测试报告汇总名称: CAN总线逆向测试-CAN5
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN_001
  测试报告汇总名称: CAN总线逆向测试-CAN5
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: /
  测试报告汇总名称: 缺失
  测试过程名称: 多路CAN信息汇总
  ---
  对比: 测试过程 vs 测试用例
  用例编号: /
  测试过程名称: 多路CAN信息汇总
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN_004
  测试报告汇总名称: CAN总线模糊测试-CAN5
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN_004
  测试报告汇总名称: CAN总线模糊测试-CAN5
  测试用例名称: CAN总线模糊测试
  ---

处理文件夹: LK1A_CDC
Word文件: LK1A_CDC_8295渗透测试报告V2.1.1.docx
Excel文件: LK1A_CDC_8295测试用例.xlsx
测试报告汇总数据量: 1
测试过程数据量: 81
测试用例数据量: 82

=== 处理文件夹: LK1A_CDC ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-FS-AS-APP-001
  测试报告汇总名称: 缺失
  测试过程名称: APP应用篡改测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-FS-AS-APP-001
  测试报告汇总名称: 缺失
  测试用例名称: APP应用篡改测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-BS-RD-005
  测试报告汇总名称: 缺失
  测试过程名称: 10服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-BS-RD-005
  测试报告汇总名称: 缺失
  测试用例名称: 10服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-008
  测试报告汇总名称: 缺失
  测试过程名称: 驱动配置安全测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-008
  测试报告汇总名称: 缺失
  测试用例名称: 驱动配置安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-BT-002
  测试报告汇总名称: 缺失
  测试过程名称: 蓝牙配对模式安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-BT-002
  测试报告汇总名称: 缺失
  测试用例名称: 蓝牙配对模式安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-BT-006
  测试报告汇总名称: 缺失
  测试过程名称: 蓝牙日志信息查看
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-BT-006
  测试报告汇总名称: 缺失
  测试用例名称: 蓝牙日志信息查看
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-ETH-009
  测试报告汇总名称: 缺失
  测试过程名称: 以太网服务认证测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-ETH-009
  测试报告汇总名称: 缺失
  测试用例名称: 以太网服务认证测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-DS-003
  测试报告汇总名称: 缺失
  测试过程名称: 安全日志
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-DS-003
  测试报告汇总名称: 缺失
  测试用例名称: 安全日志
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-BS-RD-007
  测试报告汇总名称: 缺失
  测试过程名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-BS-RD-007
  测试报告汇总名称: 缺失
  测试用例名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-FS-AS-BIN-002
  测试报告汇总名称: 缺失
  测试过程名称: 固件逆向测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-FS-AS-BIN-002
  测试报告汇总名称: 缺失
  测试用例名称: 固件逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-HS-USB-002
  测试报告汇总名称: 缺失
  测试过程名称: 离线包签名篡改
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-HS-USB-002
  测试报告汇总名称: 缺失
  测试用例名称: 离线包签名篡改
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-WIFI-006
  测试报告汇总名称: 缺失
  测试过程名称: WiFi模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-WIFI-006
  测试报告汇总名称: 缺失
  测试用例名称: WiFi模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-014
  测试报告汇总名称: 缺失
  测试过程名称: 应用授权管理测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-014
  测试报告汇总名称: 缺失
  测试用例名称: 应用授权管理测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-006
  测试报告汇总名称: 缺失
  测试过程名称: 防火墙配置测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-006
  测试报告汇总名称: 缺失
  测试用例名称: 防火墙配置测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-HS-USB-004
  测试报告汇总名称: 缺失
  测试过程名称: USB接口功能分析
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-HS-USB-004
  测试报告汇总名称: 缺失
  测试用例名称: USB接口功能分析
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-005
  测试报告汇总名称: 缺失
  测试过程名称: 应用安装防护测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-005
  测试报告汇总名称: 缺失
  测试用例名称: 应用安装防护测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-FS-AS-APP-005
  测试报告汇总名称: 缺失
  测试过程名称: 应用安装安全测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-FS-AS-APP-005
  测试报告汇总名称: 缺失
  测试用例名称: 应用安装安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-BS-RD-003
  测试报告汇总名称: 缺失
  测试过程名称: seed随机度安全测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-BS-RD-003
  测试报告汇总名称: 缺失
  测试用例名称: seed随机度安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-ETH-008
  测试报告汇总名称: 缺失
  测试过程名称: SOME/IP模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-ETH-008
  测试报告汇总名称: 缺失
  测试用例名称: SOME/IP模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-004
  测试报告汇总名称: 缺失
  测试过程名称: 安全配置测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-004
  测试报告汇总名称: 缺失
  测试用例名称: 安全配置测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-BT-005
  测试报告汇总名称: 缺失
  测试过程名称: 蓝牙拒绝服务测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-BT-005
  测试报告汇总名称: 缺失
  测试用例名称: 蓝牙拒绝服务测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-VTC-001
  测试报告汇总名称: 缺失
  测试过程名称: 车云通信协议安全测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-VTC-001
  测试报告汇总名称: 缺失
  测试用例名称: 车云通信协议安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-CAN-004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-CAN-004
  测试报告汇总名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-WIFI-005
  测试报告汇总名称: 缺失
  测试过程名称: WiFi密码配置是否密码安全等级提示
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-WIFI-005
  测试报告汇总名称: 缺失
  测试用例名称: WiFi密码配置是否密码安全等级提示
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-012
  测试报告汇总名称: 缺失
  测试过程名称: 系统应用后门查看
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-012
  测试报告汇总名称: 缺失
  测试用例名称: 系统应用后门查看
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-ETH-002
  测试报告汇总名称: 缺失
  测试过程名称: 以太网设备传输安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-ETH-002
  测试报告汇总名称: 缺失
  测试用例名称: 以太网设备传输安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-001
  测试报告汇总名称: 缺失
  测试过程名称: 安全启动开启
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-001
  测试报告汇总名称: 缺失
  测试用例名称: 安全启动开启
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-HS-YJJK-001
  测试报告汇总名称: 缺失
  测试过程名称: 调试接口暴露测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-HS-YJJK-001
  测试报告汇总名称: 缺失
  测试用例名称: 调试接口暴露测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-ETH-005
  测试报告汇总名称: 缺失
  测试过程名称: 5555ADB连接端口安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-ETH-005
  测试报告汇总名称: 缺失
  测试用例名称: 5555ADB连接端口安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-DS-006
  测试报告汇总名称: 缺失
  测试过程名称: 数据传输
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-DS-006
  测试报告汇总名称: 缺失
  测试用例名称: 数据传输
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-WIFI-002
  测试报告汇总名称: 缺失
  测试过程名称: WiFi接入设备端口检测
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-WIFI-002
  测试报告汇总名称: 缺失
  测试用例名称: WiFi接入设备端口检测
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-HS-YJJK-002
  测试报告汇总名称: 缺失
  测试过程名称: 调试接口安全防护测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-HS-YJJK-002
  测试报告汇总名称: 缺失
  测试用例名称: 调试接口安全防护测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-007
  测试报告汇总名称: 缺失
  测试过程名称: ADB提权测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-007
  测试报告汇总名称: 缺失
  测试用例名称: ADB提权测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-BT-010
  测试报告汇总名称: 缺失
  测试过程名称: 短距离通信口令唯一性测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-BT-010
  测试报告汇总名称: 缺失
  测试用例名称: 短距离通信口令唯一性测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-ETH-001
  测试报告汇总名称: 缺失
  测试过程名称: 以太网拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-ETH-001
  测试报告汇总名称: 缺失
  测试用例名称: 以太网拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-HS-USB-003
  测试报告汇总名称: 缺失
  测试过程名称: 固件更新逻辑分析
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-HS-USB-003
  测试报告汇总名称: 缺失
  测试用例名称: 固件更新逻辑分析
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-003
  测试报告汇总名称: 缺失
  测试过程名称: Secure boot真实性测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-003
  测试报告汇总名称: 缺失
  测试用例名称: Secure boot真实性测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-002
  测试报告汇总名称: 缺失
  测试过程名称: Secure boot完整性测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-002
  测试报告汇总名称: 缺失
  测试用例名称: Secure boot完整性测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-ETH-004
  测试报告汇总名称: 缺失
  测试过程名称: 111端口安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-ETH-004
  测试报告汇总名称: 缺失
  测试用例名称: 111端口安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-FS-AS-APP-003
  测试报告汇总名称: 缺失
  测试过程名称: 应用数据安全测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-FS-AS-APP-003
  测试报告汇总名称: 缺失
  测试用例名称: 应用数据安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-BS-RD-006
  测试报告汇总名称: 缺失
  测试过程名称: 23服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-BS-RD-006
  测试报告汇总名称: 缺失
  测试用例名称: 23服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-BT-009
  测试报告汇总名称: 缺失
  测试用例名称: 蓝牙通信连接设备占用测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT-INCUS-BT-009
  测试过程名称: 缺失
  测试用例名称: 蓝牙通信连接设备占用测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-013
  测试报告汇总名称: 缺失
  测试过程名称: 系统默认用户权限
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-013
  测试报告汇总名称: 缺失
  测试用例名称: 系统默认用户权限
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-DS-001
  测试报告汇总名称: 缺失
  测试过程名称: 配置文件测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-DS-001
  测试报告汇总名称: 缺失
  测试用例名称: 配置文件测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-ETH-003
  测试报告汇总名称: 缺失
  测试过程名称: 端口扫描
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-ETH-003
  测试报告汇总名称: 缺失
  测试用例名称: 端口扫描
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-FS-AS-APP-002
  测试报告汇总名称: 缺失
  测试过程名称: APP应用逆向测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-FS-AS-APP-002
  测试报告汇总名称: 缺失
  测试用例名称: APP应用逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-BS-RD-001
  测试报告汇总名称: 缺失
  测试过程名称: 诊断复位测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-BS-RD-001
  测试报告汇总名称: 缺失
  测试用例名称: 诊断复位测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-CAN-002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-CAN-002
  测试报告汇总名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-FS-AS-BIN-001
  测试报告汇总名称: 缺失
  测试过程名称: 固件提取测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-FS-AS-BIN-001
  测试报告汇总名称: 缺失
  测试用例名称: 固件提取测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-WIFI-001
  测试报告汇总名称: 缺失
  测试过程名称: WiFi认证模式安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-WIFI-001
  测试报告汇总名称: 缺失
  测试用例名称: WiFi认证模式安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-HS-USB-005
  测试报告汇总名称: 缺失
  测试过程名称: 恶意木马导入
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-HS-USB-005
  测试报告汇总名称: 缺失
  测试用例名称: 恶意木马导入
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-HS-USB-001
  测试报告汇总名称: 缺失
  测试过程名称: 离线包固件篡改
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-HS-USB-001
  测试报告汇总名称: 缺失
  测试用例名称: 离线包固件篡改
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-BS-RD-004
  测试报告汇总名称: 缺失
  测试过程名称: 读写服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-BS-RD-004
  测试报告汇总名称: 缺失
  测试用例名称: 读写服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-DS-004
  测试报告汇总名称: 缺失
  测试过程名称: 密钥安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-DS-004
  测试报告汇总名称: 缺失
  测试用例名称: 密钥安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-ETH-006
  测试报告汇总名称: 缺失
  测试过程名称: 车载以太网服务探测测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-ETH-006
  测试报告汇总名称: 缺失
  测试用例名称: 车载以太网服务探测测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-HS-YJJK-005
  测试报告汇总名称: 缺失
  测试过程名称: 调试接口固件提取测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-HS-YJJK-005
  测试报告汇总名称: 缺失
  测试用例名称: 调试接口固件提取测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-GPS-002
  测试报告汇总名称: 缺失
  测试过程名称: GNSS定位干扰
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-GPS-002
  测试报告汇总名称: 缺失
  测试用例名称: GNSS定位干扰
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-WIFI-003
  测试报告汇总名称: 缺失
  测试过程名称: WiFi Deauth攻击测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-WIFI-003
  测试报告汇总名称: 缺失
  测试用例名称: WiFi Deauth攻击测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-CAN-003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-CAN-003
  测试报告汇总名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-DS-002
  测试报告汇总名称: 缺失
  测试过程名称: 敏感数据存储测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-DS-002
  测试报告汇总名称: 缺失
  测试用例名称: 敏感数据存储测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-BS-RD-002
  测试报告汇总名称: 缺失
  测试过程名称: 诊断服务安全测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-BS-RD-002
  测试报告汇总名称: 缺失
  测试用例名称: 诊断服务安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-GPS-001
  测试报告汇总名称: 缺失
  测试过程名称: GNSS定位劫持
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-GPS-001
  测试报告汇总名称: 缺失
  测试用例名称: GNSS定位劫持
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-BT-008
  测试报告汇总名称: 缺失
  测试过程名称: 短距离通信车载蓝牙安全连接测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-BT-008
  测试报告汇总名称: 缺失
  测试用例名称: 短距离通信车载蓝牙安全连接测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-010
  测试报告汇总名称: 缺失
  测试过程名称: 终端命令执行权限测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-010
  测试报告汇总名称: 缺失
  测试用例名称: 终端命令执行权限测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-BS-RD-008
  测试报告汇总名称: 缺失
  测试过程名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-BS-RD-008
  测试报告汇总名称: 缺失
  测试用例名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-011
  测试报告汇总名称: 缺失
  测试过程名称: 终端日志审计日志存储测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-011
  测试报告汇总名称: 缺失
  测试用例名称: 终端日志审计日志存储测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-BT-001
  测试报告汇总名称: 缺失
  测试过程名称: 蓝牙格式化字符串漏洞
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-BT-001
  测试报告汇总名称: 缺失
  测试用例名称: 蓝牙格式化字符串漏洞
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-WIFI-007
  测试报告汇总名称: 缺失
  测试过程名称: WiFi密码枚举测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-WIFI-007
  测试报告汇总名称: 缺失
  测试用例名称: WiFi密码枚举测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-BT-003
  测试报告汇总名称: 缺失
  测试过程名称: 蓝牙已知漏洞测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-BT-003
  测试报告汇总名称: 缺失
  测试用例名称: 蓝牙已知漏洞测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-HS-YJJK-004
  测试报告汇总名称: 缺失
  测试过程名称: PCB板丝印信息暴露测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-HS-YJJK-004
  测试报告汇总名称: 缺失
  测试用例名称: PCB板丝印信息暴露测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-FS-AS-APP-004
  测试报告汇总名称: 缺失
  测试过程名称: 应用签名安全测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-FS-AS-APP-004
  测试报告汇总名称: 缺失
  测试用例名称: 应用签名安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-CAN-001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-CAN-001
  测试报告汇总名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-FS-AS-BIN-003
  测试报告汇总名称: 缺失
  测试过程名称: 固件篡改测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-FS-AS-BIN-003
  测试报告汇总名称: 缺失
  测试用例名称: 固件篡改测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-BT-007
  测试报告汇总名称: 缺失
  测试过程名称: 蓝牙通信数据加密传输安全性测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-BT-007
  测试报告汇总名称: 缺失
  测试用例名称: 蓝牙通信数据加密传输安全性测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-SS-009
  测试报告汇总名称: 缺失
  测试过程名称: 网络接口配置测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-SS-009
  测试报告汇总名称: 缺失
  测试用例名称: 网络接口配置测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-WIFI-004
  测试报告汇总名称: 缺失
  测试过程名称: WiFi版本漏洞检测
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-WIFI-004
  测试报告汇总名称: 缺失
  测试用例名称: WiFi版本漏洞检测
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-BT-004
  测试报告汇总名称: 缺失
  测试过程名称: 蓝牙模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-BT-004
  测试报告汇总名称: 缺失
  测试用例名称: 蓝牙模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-HS-YJJK-003
  测试报告汇总名称: 缺失
  测试过程名称: 调试接口内存读写测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-HS-YJJK-003
  测试报告汇总名称: 缺失
  测试用例名称: 调试接口内存读写测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-DS-005
  测试报告汇总名称: 缺失
  测试过程名称: 证书安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-DS-005
  测试报告汇总名称: 缺失
  测试用例名称: 证书安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-ETH-007
  测试报告汇总名称: 缺失
  测试过程名称: DoIP模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-ETH-007
  测试报告汇总名称: 缺失
  测试用例名称: DoIP模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-CAN-005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-CAN-005
  测试报告汇总名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-BS-RD-009
  测试报告汇总名称: 缺失
  测试过程名称: 31服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-BS-RD-009
  测试报告汇总名称: 缺失
  测试用例名称: 31服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-VTC-002
  测试报告汇总名称: 缺失
  测试过程名称: 车云通信认证安全测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-VTC-002
  测试报告汇总名称: 缺失
  测试用例名称: 车云通信认证安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: LK1A_CDC_8295
  测试报告汇总名称: 零部件
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: LK1A_CDC_8295
  测试报告汇总名称: 零部件
  测试用例名称: 缺失
  ---

处理文件夹: LK2A_CCM
Word文件: 010-5 网络安全确认测试报告_【LK2A】_【CCM】_【20250822】.docx
Excel文件: LK2A_CCM测试用例.xlsx
测试报告汇总数据量: 57
测试过程数据量: 57
测试用例数据量: 57

=== 处理文件夹: LK2A_CCM ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_003
  测试报告汇总名称: 缺失
  测试过程名称: 种子随机度安全测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_003
  测试报告汇总名称: 缺失
  测试用例名称: 种子随机度安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_006
  测试报告汇总名称: 缺失
  测试过程名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_006
  测试报告汇总名称: 缺失
  测试用例名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_007
  测试报告汇总名称: 2F服务安全
  测试过程名称: 31服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_007
  测试报告汇总名称: 2F服务安全
  测试用例名称: 31服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_004
  测试报告汇总名称: 种子随机度安全测试
  测试过程名称: 读写服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_004
  测试报告汇总名称: 种子随机度安全测试
  测试用例名称: 读写服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_009
  测试报告汇总名称: 31服务安全
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_009
  测试报告汇总名称: 31服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_005
  测试报告汇总名称: 读写服务安全
  测试过程名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_005
  测试报告汇总名称: 读写服务安全
  测试用例名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_008
  测试报告汇总名称: 27服务安全
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_008
  测试报告汇总名称: 27服务安全
  测试用例名称: 缺失
  ---

处理文件夹: LK2A_TCU
Word文件: 010-5 网络安全确认测试报告_【LK2A】_【TCU】_【20250904】.docx
Excel文件: LK2A_TCU测试用例.xlsx
测试报告汇总数据量: 62
测试过程数据量: 62
测试用例数据量: 62

=== 处理文件夹: LK2A_TCU ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_BLE_002
  测试报告汇总名称: BLE pin 码嗅探测试-高配
  测试过程名称: BLE pin 码嗅探测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_BLE_002
  测试报告汇总名称: BLE pin 码嗅探测试-高配
  测试用例名称: BLE pin 码嗅探测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_HW_002
  测试报告汇总名称: 调试接口暴露测试-低配
  测试过程名称: 调试接口暴露测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_HW_002
  测试报告汇总名称: 调试接口暴露测试-低配
  测试用例名称: 调试接口暴露测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_HW_005
  测试报告汇总名称: emmc芯片固件提取-低配
  测试过程名称: emmc芯片固件提取
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_HW_005
  测试报告汇总名称: emmc芯片固件提取-低配
  测试用例名称: emmc芯片固件提取
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_HW_004
  测试报告汇总名称: 调试接口固件提取测试-低配
  测试过程名称: 调试接口固件提取测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_HW_004
  测试报告汇总名称: 调试接口固件提取测试-低配
  测试用例名称: 调试接口固件提取测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_BLE_001
  测试报告汇总名称: BLE 从机连接占用测试-高配
  测试过程名称: BLE 从机连接占用测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_BLE_001
  测试报告汇总名称: BLE 从机连接占用测试-高配
  测试用例名称: BLE 从机连接占用测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_BLE_003
  测试报告汇总名称: 蓝牙漏洞测试-高配
  测试过程名称: 蓝牙漏洞测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_BLE_003
  测试报告汇总名称: 蓝牙漏洞测试-高配
  测试用例名称: 蓝牙漏洞测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_HW_003
  测试报告汇总名称: 调试接口安全防护测试-低配
  测试过程名称: 调试接口安全防护测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_HW_003
  测试报告汇总名称: 调试接口安全防护测试-低配
  测试用例名称: 调试接口安全防护测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_HW_001
  测试报告汇总名称: 丝印信息泄露-低配
  测试过程名称: 丝印信息泄露
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_HW_001
  测试报告汇总名称: 丝印信息泄露-低配
  测试用例名称: 丝印信息泄露
  ---

处理文件夹: PK1B_ADCU
Word文件: 网络安全确认测试报告_【PK1B】_【ADCU】_【20250829】.docx
Excel文件: PK1B_ADCU测试用例.xlsx
测试报告汇总数据量: 93
测试过程数据量: 102
测试用例数据量: 101

=== 处理文件夹: PK1B_ADCU ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_008 _DoIP
  测试报告汇总名称: 缺失
  测试过程名称: 31服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_008 _DoIP
  测试过程名称: 31服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN6_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN6
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN6_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN6
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN6_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ETH_004
  测试报告汇总名称: DoIP模糊测试
  测试过程名称: SOME/IP模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_ETH_004
  测试报告汇总名称: DoIP模糊测试
  测试用例名称: SOME/IP模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_002
  测试报告汇总名称: 缺失
  测试用例名称: 诊断复位测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_002
  测试过程名称: 缺失
  测试用例名称: 诊断复位测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN4_002
  测试报告汇总名称: CAN总线重放测试-CAN4
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN4_002
  测试报告汇总名称: CAN总线重放测试-CAN4
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN4_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN8_004
  测试报告汇总名称: CAN总线模糊测试-CAN8
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN8_004
  测试报告汇总名称: CAN总线模糊测试-CAN8
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN8_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_003
  测试报告汇总名称: 缺失
  测试用例名称: 种子随机度安全测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_003
  测试过程名称: 缺失
  测试用例名称: 种子随机度安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN3_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN3_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN5_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN5
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN5_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN5
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN5_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN4_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN4_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_008 _CAN
  测试报告汇总名称: 缺失
  测试过程名称: 31服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_008 _CAN
  测试过程名称: 31服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_002_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 诊断复位测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_002_CAN
  测试过程名称: 缺失
  测试用例名称: 诊断复位测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_006
  测试报告汇总名称: 缺失
  测试用例名称: 2F服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_006
  测试过程名称: 缺失
  测试用例名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN6_005
  测试报告汇总名称: XCP协议扫描测试-CAN6
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN6_005
  测试报告汇总名称: XCP协议扫描测试-CAN6
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN6_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_003 _CAN
  测试报告汇总名称: 种子随机度安全测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_003 _CAN
  测试过程名称: 种子随机度安全测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN8_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN8_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN4_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN4_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_004_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 读写服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_004_CAN
  测试过程名称: 缺失
  测试用例名称: 读写服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_USB_001
  测试报告汇总名称: 缺失
  测试过程名称: 离线包完整性测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_USB_001
  测试报告汇总名称: 缺失
  测试用例名称: 离线包完整性测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_USB_002
  测试报告汇总名称: 缺失
  测试过程名称: 离线包真实性测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_USB_002
  测试报告汇总名称: 缺失
  测试用例名称: 离线包真实性测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN3_001
  测试报告汇总名称: CAN总线逆向测试-CAN3
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN3_001
  测试报告汇总名称: CAN总线逆向测试-CAN3
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN3_001
  测试过程名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN3_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN3_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN4_004
  测试报告汇总名称: CAN总线模糊测试-CAN4
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN4_004
  测试报告汇总名称: CAN总线模糊测试-CAN4
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN4_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN6_001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN6_001
  测试过程名称: CAN总线逆向测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN2_005
  测试报告汇总名称: XCP协议扫描测试-CAN2
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN2_005
  测试报告汇总名称: XCP协议扫描测试-CAN2
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN2_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN3_005
  测试报告汇总名称: XCP协议扫描测试-CAN3
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN3_005
  测试报告汇总名称: XCP协议扫描测试-CAN3
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN3_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ETH_005
  测试报告汇总名称: SOME/IP模糊测试
  测试过程名称: 以太网设备传输安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_ETH_005
  测试报告汇总名称: SOME/IP模糊测试
  测试用例名称: 以太网设备传输安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN4_001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN4_001
  测试过程名称: CAN总线逆向测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_004 _CAN
  测试报告汇总名称: 读写服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_004 _CAN
  测试过程名称: 读写服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN6_004
  测试报告汇总名称: CAN总线模糊测试-CAN6
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN6_004
  测试报告汇总名称: CAN总线模糊测试-CAN6
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN6_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_005_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 23服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_005_CAN
  测试过程名称: 缺失
  测试用例名称: 23服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN8_005
  测试报告汇总名称: XCP协议扫描测试-CAN8
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN8_005
  测试报告汇总名称: XCP协议扫描测试-CAN8
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN8_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ETH_002
  测试报告汇总名称: 以太网端口扫描
  测试过程名称: 以太网危险端口测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_ETH_002
  测试报告汇总名称: 以太网端口扫描
  测试用例名称: 以太网危险端口测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_005
  测试报告汇总名称: 缺失
  测试用例名称: 23服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_005
  测试过程名称: 缺失
  测试用例名称: 23服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_006 _DoIP
  测试报告汇总名称: 27服务安全
  测试过程名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_006 _DoIP
  测试报告汇总名称: 27服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_006 _DoIP
  测试过程名称: 2F服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN2_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN2_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN6_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN6_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN5_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN5_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_002 _DoIP
  测试报告汇总名称: 诊断复位测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_002 _DoIP
  测试过程名称: 诊断复位测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN3_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN3
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN3_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN3
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN3_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_USB_005
  测试报告汇总名称: 缺失
  测试过程名称: 固件更新逻辑分析
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_USB_005
  测试报告汇总名称: 缺失
  测试用例名称: 固件更新逻辑分析
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_005 _DoIP
  测试报告汇总名称: 2F服务安全
  测试过程名称: 23服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_005 _DoIP
  测试报告汇总名称: 2F服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_005 _DoIP
  测试过程名称: 23服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN5_005
  测试报告汇总名称: XCP协议扫描测试-CAN5
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN5_005
  测试报告汇总名称: XCP协议扫描测试-CAN5
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN5_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN8_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN8_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_008_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 31服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_008_CAN
  测试过程名称: 缺失
  测试用例名称: 31服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_USB_006
  测试报告汇总名称: 缺失
  测试过程名称: 恶意木马导入
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_USB_006
  测试报告汇总名称: 缺失
  测试用例名称: 恶意木马导入
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN5_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN5_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_001 _DoIP
  测试报告汇总名称: 研发生产调试命令去除测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_001 _DoIP
  测试过程名称: 研发生产调试命令去除测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_001_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 研发生产调试命令去除测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_001_CAN
  测试过程名称: 缺失
  测试用例名称: 研发生产调试命令去除测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN4_005
  测试报告汇总名称: XCP协议扫描测试-CAN4
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN4_005
  测试报告汇总名称: XCP协议扫描测试-CAN4
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN4_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_007_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 27服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_007_CAN
  测试过程名称: 缺失
  测试用例名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_008
  测试报告汇总名称: 缺失
  测试用例名称: 31服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_008
  测试过程名称: 缺失
  测试用例名称: 31服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN4_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN4_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN6_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN6_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN2_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN2_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN8_002
  测试报告汇总名称: CAN总线重放测试-CAN8
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN8_002
  测试报告汇总名称: CAN总线重放测试-CAN8
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN8_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_001 _CAN
  测试报告汇总名称: 研发生产调试命令去除测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_001 _CAN
  测试过程名称: 研发生产调试命令去除测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN5_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN5_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN7_001
  测试报告汇总名称: CAN总线逆向测试-CAN7
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN7_001
  测试报告汇总名称: CAN总线逆向测试-CAN7
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN7_001
  测试过程名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_002 _CAN
  测试报告汇总名称: 诊断复位测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_002 _CAN
  测试过程名称: 诊断复位测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN6_002
  测试报告汇总名称: CAN总线重放测试-CAN6
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN6_002
  测试报告汇总名称: CAN总线重放测试-CAN6
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN6_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN3_001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN3_001
  测试过程名称: CAN总线逆向测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN3_004
  测试报告汇总名称: CAN总线模糊测试-CAN3
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN3_004
  测试报告汇总名称: CAN总线模糊测试-CAN3
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN3_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN6_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN6_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN7_002
  测试报告汇总名称: CAN总线重放测试-CAN7
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN7_002
  测试报告汇总名称: CAN总线重放测试-CAN7
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN7_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN2_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN2_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN7_004
  测试报告汇总名称: CAN总线模糊测试-CAN7
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN7_004
  测试报告汇总名称: CAN总线模糊测试-CAN7
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN7_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_007 _CAN
  测试报告汇总名称: 31服务安全
  测试过程名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_007 _CAN
  测试报告汇总名称: 31服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_007 _CAN
  测试过程名称: 27服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN6_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN6_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN2_002
  测试报告汇总名称: CAN总线重放测试-CAN2
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN2_002
  测试报告汇总名称: CAN总线重放测试-CAN2
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN2_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN4_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN4_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_007
  测试报告汇总名称: 缺失
  测试用例名称: 27服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_007
  测试过程名称: 缺失
  测试用例名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN7_005
  测试报告汇总名称: XCP协议扫描测试-CAN7
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN7_005
  测试报告汇总名称: XCP协议扫描测试-CAN7
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN7_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN2_004
  测试报告汇总名称: CAN总线模糊测试-CAN2
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN2_004
  测试报告汇总名称: CAN总线模糊测试-CAN2
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN2_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN8_001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN8_001
  测试过程名称: CAN总线逆向测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN2_001
  测试报告汇总名称: CAN总线逆向测试-CAN2
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN2_001
  测试报告汇总名称: CAN总线逆向测试-CAN2
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN8_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN8_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_006 _CAN
  测试报告汇总名称: 27服务安全
  测试过程名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_006 _CAN
  测试报告汇总名称: 27服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_006 _CAN
  测试过程名称: 2F服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_003_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 种子随机度安全测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_003_CAN
  测试过程名称: 缺失
  测试用例名称: 种子随机度安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN8_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN8_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_004
  测试报告汇总名称: 缺失
  测试用例名称: 读写服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_004
  测试过程名称: 缺失
  测试用例名称: 读写服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN4_001
  测试报告汇总名称: CAN总线逆向测试-CAN4
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN4_001
  测试报告汇总名称: CAN总线逆向测试-CAN4
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN4_001
  测试过程名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN5_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN5_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN5_001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN5_001
  测试过程名称: CAN总线逆向测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN7_001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN7_001
  测试过程名称: CAN总线逆向测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN2_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN2
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN2_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN2
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN2_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_USB_003
  测试报告汇总名称: 缺失
  测试过程名称: USB接口功能分析
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_USB_003
  测试报告汇总名称: 缺失
  测试用例名称: USB接口功能分析
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN5_002
  测试报告汇总名称: CAN总线重放测试-CAN5
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN5_002
  测试报告汇总名称: CAN总线重放测试-CAN5
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN5_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN8_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN8
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN8_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN8
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN8_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_USB_004
  测试报告汇总名称: 缺失
  测试过程名称: USB文件模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_USB_004
  测试报告汇总名称: 缺失
  测试用例名称: USB文件模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_001
  测试报告汇总名称: 缺失
  测试用例名称: 研发生产调试命令去除测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_001
  测试过程名称: 缺失
  测试用例名称: 研发生产调试命令去除测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN7_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN7_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN3_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN3_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_007 _DoIP
  测试报告汇总名称: 31服务安全
  测试过程名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_007 _DoIP
  测试报告汇总名称: 31服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_007 _DoIP
  测试过程名称: 27服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN2_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN2_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: /
  测试报告汇总名称: 缺失
  测试过程名称: 多路CAN信息汇总
  ---
  对比: 测试过程 vs 测试用例
  用例编号: /
  测试过程名称: 多路CAN信息汇总
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN3_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN3_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ETH_003
  测试报告汇总名称: 以太网危险端口测试
  测试过程名称: DoIP模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_ETH_003
  测试报告汇总名称: 以太网危险端口测试
  测试用例名称: DoIP模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN6_001
  测试报告汇总名称: CAN总线逆向测试-CAN6
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN6_001
  测试报告汇总名称: CAN总线逆向测试-CAN6
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN6_001
  测试过程名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_006_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 2F服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_006_CAN
  测试过程名称: 缺失
  测试用例名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_003 _DoIP
  测试报告汇总名称: 种子随机度安全测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_003 _DoIP
  测试过程名称: 种子随机度安全测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_004 _DoIP
  测试报告汇总名称: 读写服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_004 _DoIP
  测试过程名称: 读写服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN5_001
  测试报告汇总名称: CAN总线逆向测试-CAN5
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN5_001
  测试报告汇总名称: CAN总线逆向测试-CAN5
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN5_001
  测试过程名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_005 _CAN
  测试报告汇总名称: 2F服务安全
  测试过程名称: 23服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_005 _CAN
  测试报告汇总名称: 2F服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_005 _CAN
  测试过程名称: 23服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN7_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN7_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN5_004
  测试报告汇总名称: CAN总线模糊测试-CAN5
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN5_004
  测试报告汇总名称: CAN总线模糊测试-CAN5
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN5_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN7_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN7_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN4_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN4
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN4_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN4
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN4_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN7_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN7_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN8_001
  测试报告汇总名称: CAN总线逆向测试-CAN8
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN8_001
  测试报告汇总名称: CAN总线逆向测试-CAN8
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN8_001
  测试过程名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN7_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN7
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN7_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN7
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN7_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN3_002
  测试报告汇总名称: CAN总线重放测试-CAN3
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN3_002
  测试报告汇总名称: CAN总线重放测试-CAN3
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN3_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---

处理文件夹: PK1B_L2
Word文件: 网络安全确认测试报告_【PK1B L2】_【整车】_【20250912】.docx
Excel文件: PK1B L2整车测试用例.xlsx
测试报告汇总数据量: 80
测试过程数据量: 80
测试用例数据量: 80

=== 处理文件夹: PK1B_L2 ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_BTK_006
  测试报告汇总名称: 多模蓝牙钥匙模糊测试
  测试过程名称: 蓝牙钥匙模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_BTK_006
  测试报告汇总名称: 多模蓝牙钥匙模糊测试
  测试用例名称: 蓝牙钥匙模糊测试
  ---

处理文件夹: PK1B_L4
Word文件: 网络安全确认测试报告_【PK1B L4】_【整车】_【20250904】.docx
Excel文件: PK1B L4整车测试用例.xlsx
测试报告汇总数据量: 44
测试过程数据量: 44
测试用例数据量: 44

=== 处理文件夹: PK1B_L4 ===
  所有数据一致！

==================================================
总结报告
==================================================
发现不一致的文件夹:
  Fr_camera: 12 个不一致项
  LK1A_CDC: 166 个不一致项
  LK2A_CCM: 14 个不一致项
  LK2A_TCU: 16 个不一致项
  PK1B_ADCU: 264 个不一致项
  PK1B_L2: 2 个不一致项

报告生成完成！
详细结果已保存到: 测试数据对比结果_20250918_144543.txt
