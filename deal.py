import os
import pandas as pd
from docx import Document
from datetime import datetime

def extract_test_summary_data(doc_path, folder_name):
    """提取Word文档测试报告汇总数据"""
    try:
        doc = Document(doc_path)
        test_summary_data = {}

        # 针对LK1A_CDC文件夹的特殊处理
        if folder_name == "LK1A_CDC":
            if len(doc.tables) < 3:
                print(f"警告: {doc_path} 表格数量不足，无法提取表格2")
                return {}

            table = doc.tables[2]  # 第三个表格（索引为2）

            # 从第二行开始提取（第一行是表头）
            for row in table.rows[1:]:
                if len(row.cells) >= 4:  # 确保有足够的列
                    test_id = row.cells[1].text.strip()    # 第二列是用例编号
                    test_name = row.cells[3].text.strip()  # 第四列是测试用例名称
                    if test_id and test_name:  # 确保不是空值
                        test_summary_data[test_id] = test_name
        else:
            # 其他文件夹的默认处理
            if len(doc.tables) < 6:
                print(f"警告: {doc_path} 表格数量不足，无法提取表格5")
                return {}

            table = doc.tables[5]  # 第六个表格（索引为5）

            # 从第二行开始提取（第一行是表头）
            for row in table.rows[1:]:
                if len(row.cells) >= 2:
                    test_id = row.cells[0].text.strip()   # 第一列是用例编号
                    test_name = row.cells[1].text.strip() # 第二列是测试用例名称
                    if test_id and test_name:  # 确保不是空值
                        test_summary_data[test_id] = test_name

        return test_summary_data
    except Exception as e:
        print(f"提取Word文档测试报告汇总数据时出错 {doc_path}: {e}")
        return {}

def extract_test_process_data(doc_path, folder_name):
    """提取Word文档测试过程数据"""
    try:
        doc = Document(doc_path)
        test_process_data = {}

        # 针对LK1A_CDC文件夹的特殊处理
        if folder_name == "LK1A_CDC":
            if len(doc.tables) < 7:
                print(f"警告: {doc_path} 表格数量不足，无法提取表格6到-4")
                return {}

            # 提取表格6到-4范围的数据，但使用不同的单元格位置
            for each_tab in doc.tables[6:-4]:
                if len(each_tab.rows) >= 2 and len(each_tab.rows[0].cells) >= 2 and len(each_tab.rows[1].cells) >= 2:
                    test_id = each_tab.cell(0, 1).text.strip()   # 第1行第2列
                    test_name = each_tab.cell(1, 1).text.strip() # 第2行第2列
                    if test_id and test_name:  # 确保不是空值
                        test_process_data[test_id] = test_name
        else:
            # 其他文件夹的默认处理
            if len(doc.tables) < 7:
                print(f"警告: {doc_path} 表格数量不足，无法提取表格6到-4")
                return {}

            # 提取表格6到-4范围的数据
            for each_tab in doc.tables[6:-4]:
                if len(each_tab.rows) >= 2 and len(each_tab.rows[0].cells) >= 2 and len(each_tab.rows[1].cells) >= 2:
                    test_id = each_tab.cell(0, 1).text.strip()
                    test_name = each_tab.cell(1, 1).text.strip()
                    if test_id and test_name:  # 确保不是空值
                        test_process_data[test_id] = test_name

        return test_process_data
    except Exception as e:
        print(f"提取Word文档测试过程数据时出错 {doc_path}: {e}")
        return {}

def extract_test_case_data(excel_path):
    """提取Excel文件的数据 (测试用例)"""
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        test_case_data = {}

        # 检查列数是否足够
        if df.shape[1] < 4:
            print(f"警告: {excel_path} 列数不足，无法提取第3、4列")
            return {}

        # 第三列和第四列分别是测试用例编号和测试用例名称（索引2和3）
        for _, row in df.iterrows():
            test_id = str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else ""
            test_name = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ""
            if test_id and test_name and test_id != 'nan':  # 确保不是空值或NaN
                test_case_data[test_id] = test_name

        return test_case_data
    except Exception as e:
        print(f"提取Excel文件数据时出错 {excel_path}: {e}")
        return {}

def compare_data(test_summary_data, test_process_data, test_case_data, folder_name, output_file):
    """对比三组数据的一致性"""
    result_text = f"\n=== 处理文件夹: {folder_name} ===\n"
    print(result_text.strip())
    output_file.write(result_text)

    # 获取所有用例编号的并集
    all_test_ids = set(test_summary_data.keys()) | set(test_process_data.keys()) | set(test_case_data.keys())

    inconsistencies = []

    for test_id in all_test_ids:
        summary_name = test_summary_data.get(test_id, "缺失")
        process_name = test_process_data.get(test_id, "缺失")
        case_name = test_case_data.get(test_id, "缺失")

        # 检查测试报告汇总与测试过程是否一致
        if summary_name != process_name:
            inconsistencies.append({
                'comparison': '测试报告汇总 vs 测试过程',
                'test_id': test_id,
                'summary_name': summary_name,
                'process_name': process_name
            })

        # 检查测试报告汇总与测试用例是否一致
        if summary_name != case_name:
            inconsistencies.append({
                'comparison': '测试报告汇总 vs 测试用例',
                'test_id': test_id,
                'summary_name': summary_name,
                'case_name': case_name
            })

        # 检查测试过程与测试用例是否一致
        if process_name != case_name:
            inconsistencies.append({
                'comparison': '测试过程 vs 测试用例',
                'test_id': test_id,
                'process_name': process_name,
                'case_name': case_name
            })

    # 打印和写入不一致的结果
    if inconsistencies:
        result_text = "发现不一致项:\n"
        print(result_text.strip())
        output_file.write(result_text)

        for item in inconsistencies:
            item_text = f"  对比: {item['comparison']}\n"
            item_text += f"  用例编号: {item['test_id']}\n"

            if 'summary_name' in item and 'process_name' in item:
                item_text += f"  测试报告汇总名称: {item['summary_name']}\n"
                item_text += f"  测试过程名称: {item['process_name']}\n"
            elif 'summary_name' in item and 'case_name' in item:
                item_text += f"  测试报告汇总名称: {item['summary_name']}\n"
                item_text += f"  测试用例名称: {item['case_name']}\n"
            elif 'process_name' in item and 'case_name' in item:
                item_text += f"  测试过程名称: {item['process_name']}\n"
                item_text += f"  测试用例名称: {item['case_name']}\n"

            item_text += "  ---\n"

            print(item_text.strip())
            output_file.write(item_text)
    else:
        result_text = "  所有数据一致！\n"
        print(result_text.strip())
        output_file.write(result_text)

    return inconsistencies

def main():
    """主函数"""
    current_dir = "."

    # 创建输出文件名（包含时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"测试数据对比结果_{timestamp}.txt"

    # 获取所有文件夹
    folders = [f for f in os.listdir(current_dir)
              if os.path.isdir(os.path.join(current_dir, f)) and not f.startswith('.')]

    with open(output_filename, 'w', encoding='utf-8') as output_file:
        # 写入文件头信息
        header = f"测试数据对比结果报告\n"
        header += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        header += f"处理文件夹数量: {len(folders)}\n"
        header += f"文件夹列表: {', '.join(folders)}\n"
        header += "="*80 + "\n\n"

        print(header.strip())
        output_file.write(header)

        all_inconsistencies = {}

        for folder in folders:
            folder_path = os.path.join(current_dir, folder)

            # 查找Word和Excel文件
            word_file = None
            excel_file = None

            for file in os.listdir(folder_path):
                if file.endswith('.docx') and not file.startswith('~$'):
                    word_file = os.path.join(folder_path, file)
                elif file.endswith('.xlsx'):
                    excel_file = os.path.join(folder_path, file)

            if not word_file or not excel_file:
                warning_text = f"警告: 文件夹 {folder} 中缺少Word或Excel文件\n"
                print(warning_text.strip())
                output_file.write(warning_text)
                continue

            folder_info = f"\n处理文件夹: {folder}\n"
            folder_info += f"Word文件: {os.path.basename(word_file)}\n"
            folder_info += f"Excel文件: {os.path.basename(excel_file)}\n"

            print(folder_info.strip())
            output_file.write(folder_info)

            # 提取数据
            test_summary_data = extract_test_summary_data(word_file, folder)
            test_process_data = extract_test_process_data(word_file, folder)
            test_case_data = extract_test_case_data(excel_file)

            data_info = f"测试报告汇总数据量: {len(test_summary_data)}\n"
            data_info += f"测试过程数据量: {len(test_process_data)}\n"
            data_info += f"测试用例数据量: {len(test_case_data)}\n"

            print(data_info.strip())
            output_file.write(data_info)

            # 对比数据
            inconsistencies = compare_data(test_summary_data, test_process_data, test_case_data, folder, output_file)

            if inconsistencies:
                all_inconsistencies[folder] = inconsistencies

        # 总结报告
        summary_header = "\n" + "="*50 + "\n"
        summary_header += "总结报告\n"
        summary_header += "="*50 + "\n"

        print(summary_header.strip())
        output_file.write(summary_header)

        if all_inconsistencies:
            summary_text = "发现不一致的文件夹:\n"
            print(summary_text.strip())
            output_file.write(summary_text)

            for folder, inconsistencies in all_inconsistencies.items():
                folder_summary = f"  {folder}: {len(inconsistencies)} 个不一致项\n"
                print(folder_summary.strip())
                output_file.write(folder_summary)
        else:
            summary_text = "所有文件夹的数据都一致！\n"
            print(summary_text.strip())
            output_file.write(summary_text)

        # 写入文件尾信息
        footer = f"\n报告生成完成！\n"
        footer += f"详细结果已保存到: {output_filename}\n"

        print(footer.strip())
        output_file.write(footer)

    print(f"\n✅ 结果已导出到文件: {output_filename}")

if __name__ == "__main__":
    main()