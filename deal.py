import os
import pandas as pd
from docx import Document

def extract_test_summary_data(doc_path):
    """提取Word文档表格5的数据 (测试报告汇总)"""
    try:
        doc = Document(doc_path)
        if len(doc.tables) < 6:
            print(f"警告: {doc_path} 表格数量不足，无法提取表格5")
            return {}

        table = doc.tables[5]
        test_summary_data = {}

        # 从第二行开始提取（第一行是表头）
        for row in table.rows[1:]:
            if len(row.cells) >= 2:
                test_id = row.cells[0].text.strip()
                test_name = row.cells[1].text.strip()
                if test_id and test_name:  # 确保不是空值
                    test_summary_data[test_id] = test_name

        return test_summary_data
    except Exception as e:
        print(f"提取Word文档测试报告汇总数据时出错 {doc_path}: {e}")
        return {}

def extract_test_process_data(doc_path):
    """提取Word文档表格6到-4的数据 (测试过程)"""
    try:
        doc = Document(doc_path)
        if len(doc.tables) < 7:
            print(f"警告: {doc_path} 表格数量不足，无法提取表格6到-4")
            return {}

        test_process_data = {}

        # 提取表格6到-4范围的数据
        for each_tab in doc.tables[6:-4]:
            if len(each_tab.rows) >= 2 and len(each_tab.rows[0].cells) >= 2 and len(each_tab.rows[1].cells) >= 2:
                test_id = each_tab.cell(0, 1).text.strip()
                test_name = each_tab.cell(1, 1).text.strip()
                if test_id and test_name:  # 确保不是空值
                    test_process_data[test_id] = test_name

        return test_process_data
    except Exception as e:
        print(f"提取Word文档测试过程数据时出错 {doc_path}: {e}")
        return {}

def extract_test_case_data(excel_path):
    """提取Excel文件的数据 (测试用例)"""
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        test_case_data = {}

        # 检查列数是否足够
        if df.shape[1] < 4:
            print(f"警告: {excel_path} 列数不足，无法提取第3、4列")
            return {}

        # 第三列和第四列分别是测试用例编号和测试用例名称（索引2和3）
        for _, row in df.iterrows():
            test_id = str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else ""
            test_name = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ""
            if test_id and test_name and test_id != 'nan':  # 确保不是空值或NaN
                test_case_data[test_id] = test_name

        return test_case_data
    except Exception as e:
        print(f"提取Excel文件数据时出错 {excel_path}: {e}")
        return {}

def compare_data(test_summary_data, test_process_data, test_case_data, folder_name):
    """对比三组数据的一致性"""
    print(f"\n=== 处理文件夹: {folder_name} ===")

    # 获取所有用例编号的并集
    all_test_ids = set(test_summary_data.keys()) | set(test_process_data.keys()) | set(test_case_data.keys())

    inconsistencies = []

    for test_id in all_test_ids:
        summary_name = test_summary_data.get(test_id, "缺失")
        process_name = test_process_data.get(test_id, "缺失")
        case_name = test_case_data.get(test_id, "缺失")

        # 检查测试报告汇总与测试过程是否一致
        if summary_name != process_name:
            inconsistencies.append({
                'comparison': '测试报告汇总 vs 测试过程',
                'test_id': test_id,
                'summary_name': summary_name,
                'process_name': process_name
            })

        # 检查测试报告汇总与测试用例是否一致
        if summary_name != case_name:
            inconsistencies.append({
                'comparison': '测试报告汇总 vs 测试用例',
                'test_id': test_id,
                'summary_name': summary_name,
                'case_name': case_name
            })

        # 检查测试过程与测试用例是否一致
        if process_name != case_name:
            inconsistencies.append({
                'comparison': '测试过程 vs 测试用例',
                'test_id': test_id,
                'process_name': process_name,
                'case_name': case_name
            })

    # 打印不一致的结果
    if inconsistencies:
        print(f"发现不一致项:")
        for item in inconsistencies:
            print(f"  对比: {item['comparison']}")
            print(f"  用例编号: {item['test_id']}")
            if 'summary_name' in item and 'process_name' in item:
                print(f"  测试报告汇总名称: {item['summary_name']}")
                print(f"  测试过程名称: {item['process_name']}")
            elif 'summary_name' in item and 'case_name' in item:
                print(f"  测试报告汇总名称: {item['summary_name']}")
                print(f"  测试用例名称: {item['case_name']}")
            elif 'process_name' in item and 'case_name' in item:
                print(f"  测试过程名称: {item['process_name']}")
                print(f"  测试用例名称: {item['case_name']}")
            print("  ---")
    else:
        print("  所有数据一致！")

    return inconsistencies

def main():
    """主函数"""
    current_dir = "."

    # 获取所有文件夹
    folders = [f for f in os.listdir(current_dir)
              if os.path.isdir(os.path.join(current_dir, f)) and not f.startswith('.')]

    print(f"找到 {len(folders)} 个文件夹: {folders}")

    all_inconsistencies = {}

    for folder in folders:
        folder_path = os.path.join(current_dir, folder)

        # 查找Word和Excel文件
        word_file = None
        excel_file = None

        for file in os.listdir(folder_path):
            if file.endswith('.docx') and not file.startswith('~$'):
                word_file = os.path.join(folder_path, file)
            elif file.endswith('.xlsx'):
                excel_file = os.path.join(folder_path, file)

        if not word_file or not excel_file:
            print(f"警告: 文件夹 {folder} 中缺少Word或Excel文件")
            continue

        print(f"\n处理文件夹: {folder}")
        print(f"Word文件: {os.path.basename(word_file)}")
        print(f"Excel文件: {os.path.basename(excel_file)}")

        # 提取数据
        data_a = extract_word_data_a(word_file)
        data_b = extract_word_data_b(word_file)
        data_c = extract_excel_data_c(excel_file)

        print(f"A组数据量: {len(data_a)}")
        print(f"B组数据量: {len(data_b)}")
        print(f"C组数据量: {len(data_c)}")

        # 对比数据
        inconsistencies = compare_data(data_a, data_b, data_c, folder)

        if inconsistencies:
            all_inconsistencies[folder] = inconsistencies

    # 总结报告
    print("\n" + "="*50)
    print("总结报告")
    print("="*50)

    if all_inconsistencies:
        print("发现不一致的文件夹:")
        for folder, inconsistencies in all_inconsistencies.items():
            print(f"  {folder}: {len(inconsistencies)} 个不一致项")
    else:
        print("所有文件夹的数据都一致！")

if __name__ == "__main__":
    main()