{"cells": [{"cell_type": "markdown", "id": "666c24d3-2c2b-44f4-85db-13901107a487", "metadata": {}, "source": ["# 从报告提取用例"]}, {"cell_type": "code", "execution_count": 3, "id": "8f661aaa-9c09-4301-abac-50a152406c1c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["找到的docx文件： ['010-5 网络安全确认测试报告_【LK2A】_【CCM】_【20250822】.docx', '010-5 网络安全确认测试报告_【LK2A】_【TCU】_【20250904】.docx', 'LK1A_CDC_8295渗透测试报告V2.1.1.docx', '网络安全确认测试报告_【LK2A】_【Fr_camera】_【20250829】.docx', '网络安全确认测试报告_【PK1B L2】_【整车】_【20250904】.docx', '网络安全确认测试报告_【PK1B L4】_【整车】_【20250904】.docx', '网络安全确认测试报告_【PK1B】_【ADCU】_【20250829】.docx']\n", "保存成功: 010-5 网络安全确认测试报告_【LK2A】_【CCM】_【20250822】_提取结果.xlsx\n", "保存成功: 010-5 网络安全确认测试报告_【LK2A】_【TCU】_【20250904】_提取结果.xlsx\n", "保存成功: LK1A_CDC_8295渗透测试报告V2.1.1_提取结果.xlsx\n", "保存成功: 网络安全确认测试报告_【LK2A】_【Fr_camera】_【20250829】_提取结果.xlsx\n", "保存成功: 网络安全确认测试报告_【PK1B L2】_【整车】_【20250904】_提取结果.xlsx\n", "保存成功: 网络安全确认测试报告_【PK1B L4】_【整车】_【20250904】_提取结果.xlsx\n", "保存成功: 网络安全确认测试报告_【PK1B】_【ADCU】_【20250829】_提取结果.xlsx\n"]}], "source": ["import os\n", "from openpyxl import Workbook\n", "from docx import Document\n", "\n", "# 获取当前目录下的所有 docx 文件\n", "docx_files = [f for f in os.listdir('.') if f.endswith('.docx')]\n", "\n", "print(\"找到的docx文件：\", docx_files)\n", "\n", "for each in docx_files:\n", "    wb = Workbook()\n", "    ws = wb.active\n", "    \n", "    # 添加表头\n", "    ws.append([\"测试用例编号\", \"测试用例名称\", \"测试输入\", \"测试工具\", \"测试步骤\"])\n", "    \n", "    # 读取文档\n", "    doc = Document(each)\n", "    \n", "    # 遍历指定范围的表格\n", "    for each_tab in doc.tables[6:-4]:\n", "        test_id = each_tab.cell(0, 1).text.strip()\n", "        testname = each_tab.cell(1, 1).text.strip()\n", "        inputdata = each_tab.cell(3, 1).text.strip()\n", "        testtool = each_tab.cell(4, 1).text.strip()\n", "        teststep = each_tab.cell(5, 1).text.strip()\n", "        \n", "        row_data = [test_id, testname, inputdata, testtool, teststep]\n", "        ws.append(row_data)\n", "    \n", "    # 保存 Excel，使用原始文件名来区分\n", "    save_name = os.path.splitext(each)[0] + \"_提取结果.xlsx\"\n", "    wb.save(save_name)\n", "    print(f\"保存成功: {save_name}\")"]}, {"cell_type": "code", "execution_count": 14, "id": "e690a5ca-4128-4b88-9a0f-31b8ac930ca4", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'tuple' object has no attribute 'write'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[14], line 29\u001b[0m\n\u001b[0;32m     27\u001b[0m \u001b[38;5;66;03m# 保存 Excel，使用原始文件名来区分\u001b[39;00m\n\u001b[0;32m     28\u001b[0m save_name \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39msplitext(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mLK1A_CDC_8295渗透测试报告V2.1.1_提取结果.xlsx\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m---> 29\u001b[0m \u001b[43mwb\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msave\u001b[49m\u001b[43m(\u001b[49m\u001b[43msave_name\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     30\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m保存成功: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00msave_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\workbook.py:386\u001b[0m, in \u001b[0;36mWorkbook.save\u001b[1;34m(self, filename)\u001b[0m\n\u001b[0;32m    384\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mwrite_only \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mworksheets:\n\u001b[0;32m    385\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcreate_sheet()\n\u001b[1;32m--> 386\u001b[0m \u001b[43msave_workbook\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfilename\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\writer\\excel.py:294\u001b[0m, in \u001b[0;36msave_workbook\u001b[1;34m(workbook, filename)\u001b[0m\n\u001b[0;32m    292\u001b[0m workbook\u001b[38;5;241m.\u001b[39mproperties\u001b[38;5;241m.\u001b[39mmodified \u001b[38;5;241m=\u001b[39m datetime\u001b[38;5;241m.\u001b[39mdatetime\u001b[38;5;241m.\u001b[39mnow(tz\u001b[38;5;241m=\u001b[39mdatetime\u001b[38;5;241m.\u001b[39mtimezone\u001b[38;5;241m.\u001b[39mutc)\u001b[38;5;241m.\u001b[39mreplace(tzinfo\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[0;32m    293\u001b[0m writer \u001b[38;5;241m=\u001b[39m ExcelWriter(workbook, archive)\n\u001b[1;32m--> 294\u001b[0m \u001b[43mwriter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msave\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    295\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\writer\\excel.py:275\u001b[0m, in \u001b[0;36mExcelWriter.save\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    273\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21msave\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m    274\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Write data into the archive.\"\"\"\u001b[39;00m\n\u001b[1;32m--> 275\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwrite_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    276\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_archive\u001b[38;5;241m.\u001b[39mclose()\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\writer\\excel.py:60\u001b[0m, in \u001b[0;36mExcelWriter.write_data\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m     57\u001b[0m archive \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_archive\n\u001b[0;32m     59\u001b[0m props \u001b[38;5;241m=\u001b[39m ExtendedProperties()\n\u001b[1;32m---> 60\u001b[0m \u001b[43marchive\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwritestr\u001b[49m\u001b[43m(\u001b[49m\u001b[43mARC_APP\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtostring\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprops\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mto_tree\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     62\u001b[0m archive\u001b[38;5;241m.\u001b[39mwritestr(ARC_CORE, tostring(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mworkbook\u001b[38;5;241m.\u001b[39mproperties\u001b[38;5;241m.\u001b[39mto_tree()))\n\u001b[0;32m     63\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mworkbook\u001b[38;5;241m.\u001b[39mloaded_theme:\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipfile.py:1814\u001b[0m, in \u001b[0;36mZipFile.writestr\u001b[1;34m(self, zinfo_or_arcname, data, compress_type, compresslevel)\u001b[0m\n\u001b[0;32m   1812\u001b[0m zinfo\u001b[38;5;241m.\u001b[39mfile_size \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlen\u001b[39m(data)            \u001b[38;5;66;03m# Uncompressed size\u001b[39;00m\n\u001b[0;32m   1813\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_lock:\n\u001b[1;32m-> 1814\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mzinfo\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mw\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m dest:\n\u001b[0;32m   1815\u001b[0m         dest\u001b[38;5;241m.\u001b[39mwrite(data)\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipfile.py:1517\u001b[0m, in \u001b[0;36mZipFile.open\u001b[1;34m(self, name, mode, pwd, force_zip64)\u001b[0m\n\u001b[0;32m   1514\u001b[0m     zinfo \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgetinfo(name)\n\u001b[0;32m   1516\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m mode \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mw\u001b[39m\u001b[38;5;124m'\u001b[39m:\n\u001b[1;32m-> 1517\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_open_to_write\u001b[49m\u001b[43m(\u001b[49m\u001b[43mzinfo\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mforce_zip64\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_zip64\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1519\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_writing:\n\u001b[0;32m   1520\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCan\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt read from the ZIP file while there \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1521\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mis an open writing handle on it. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1522\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mClose the writing handle before trying to read.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipfile.py:1612\u001b[0m, in \u001b[0;36mZipFile._open_to_write\u001b[1;34m(self, zinfo, force_zip64)\u001b[0m\n\u001b[0;32m   1609\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_writecheck(zinfo)\n\u001b[0;32m   1610\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_didModify \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m-> 1612\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwrite\u001b[49m\u001b[43m(\u001b[49m\u001b[43mzinfo\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mFileHeader\u001b[49m\u001b[43m(\u001b[49m\u001b[43mzip64\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1614\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_writing \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[0;32m   1615\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m _ZipWriteFile(\u001b[38;5;28mself\u001b[39m, zinfo, zip64)\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipfile.py:762\u001b[0m, in \u001b[0;36m_Tellable.write\u001b[1;34m(self, data)\u001b[0m\n\u001b[0;32m    761\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mwrite\u001b[39m(\u001b[38;5;28mself\u001b[39m, data):\n\u001b[1;32m--> 762\u001b[0m     n \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwrite\u001b[49m(data)\n\u001b[0;32m    763\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moffset \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m n\n\u001b[0;32m    764\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m n\n", "\u001b[1;31mAttributeError\u001b[0m: 'tuple' object has no attribute 'write'"]}], "source": ["import os\n", "from openpyxl import Workbook\n", "from docx import Document\n", "\n", "# 获取当前目录下的所有 docx 文件\n", "\n", "wb = Workbook()\n", "ws = wb.active\n", "\n", "# 添加表头\n", "ws.append([\"测试项\",\"用例编号\", \"用例名称\", \"测试步骤\"])\n", "\n", "# 读取文档\n", "doc = Document('LK1A_CDC_8295渗透测试报告V2.1.1.docx')\n", "\n", "# 遍历指定范围的表格\n", "for each_tab in doc.tables[6:-4]:\n", "    testItem = each_tab.cell(2, 1).text.strip()\n", "    test_id = each_tab.cell(0, 1).text.strip()\n", "    testname = each_tab.cell(1, 1).text.strip()\n", "    testtool = each_tab.cell(4, 1).text.strip()\n", "\n", "    \n", "    row_data = [testItem, test_id, testname, testtool]\n", "    ws.append(row_data)\n", "\n", "# 保存 Excel，使用原始文件名来区分\n", "\n", "wb.save(LK1A_CDC_8295渗透测试报告V2.1.1_提取结果.xlsx)\n", "print(f\"保存成功:\")"]}, {"cell_type": "code", "execution_count": 5, "id": "e693f92c-e81f-4970-9236-668cd078a2f3", "metadata": {}, "outputs": [], "source": ["doc = Document('LK1A_CDC_8295渗透测试报告V2.1.1.docx')"]}, {"cell_type": "code", "execution_count": 12, "id": "e5d6e20c-e96a-4a20-bcd3-2f8e8d0ea11f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'PT-DS-006'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["doc.tables[-5].cell(0, 1).text"]}, {"cell_type": "code", "execution_count": null, "id": "cf84f78e-d683-4dcb-b23c-8e4a2388ba15", "metadata": {}, "outputs": [], "source": ["6"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}