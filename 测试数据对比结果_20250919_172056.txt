测试数据对比结果报告
生成时间: 2025-09-19 17:20:56
处理文件夹数量: 7
文件夹列表: Fr_camera, LK1A_CDC, LK2A_CCM, LK2A_TCU, PK1B_ADCU, PK1B_L2, PK1B_L4
================================================================================


处理文件夹: Fr_camera
Word文件: 网络安全确认测试报告_【LK2A】_【Fr_camera】_【20250829】.docx
Excel文件: 网络安全确认测试用例_【LK2A】_【Fr_camera】_【20250829】.xlsx
测试报告汇总数据量: 58
测试过程数据量: 59
测试用例数据量: 58

=== 处理文件夹: Fr_camera ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: /
  测试报告汇总名称: 缺失
  测试过程名称: 多路CAN信息汇总
  ---
  对比: 测试过程 vs 测试用例
  用例编号: /
  测试过程名称: 多路CAN信息汇总
  测试用例名称: 缺失
  ---

--- 附加检查 ---
封面修订页格式正确
漏洞统计信息正确

处理文件夹: LK1A_CDC
Word文件: LK1A_CDC_8295渗透测试报告V2.1.1.docx
Excel文件: LK1A_CDC_8295测试用例.xlsx
测试报告汇总数据量: 96
测试过程数据量: 96
测试用例数据量: 96

=== 处理文件夹: LK1A_CDC ===
  所有数据一致！

--- 附加检查 ---
跳过封面修订页检查（LK1A_CDC特殊处理）
漏洞统计信息正确

处理文件夹: LK2A_CCM
Word文件: 010-5 网络安全确认测试报告_【LK2A】_【CCM】_【20250822】.docx
Excel文件: 010-5 网络安全确认测试用例_【LK2A】_【CCM】_【20250822】.xlsx
测试报告汇总数据量: 57
测试过程数据量: 57
测试用例数据量: 57

=== 处理文件夹: LK2A_CCM ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
漏洞统计信息正确

处理文件夹: LK2A_TCU
Word文件: 网络安全确认测试报告_【LK2A】_【TCU】_【20250904】.docx
Excel文件: 网络安全确认测试用例_【LK2A】_【TCU】_【20250904】.xlsx
测试报告汇总数据量: 72
测试过程数据量: 72
测试用例数据量: 72

=== 处理文件夹: LK2A_TCU ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
漏洞统计信息正确

处理文件夹: PK1B_ADCU
Word文件: 网络安全确认测试报告_【PK1B】_【ADCU】_【20250829】.docx
Excel文件: 网络安全确认测试用例_【PK1B】_【ADCU】_【20250829】.xlsx
测试报告汇总数据量: 101
测试过程数据量: 102
测试用例数据量: 101

=== 处理文件夹: PK1B_ADCU ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: /
  测试报告汇总名称: 缺失
  测试过程名称: 多路CAN信息汇总
  ---
  对比: 测试过程 vs 测试用例
  用例编号: /
  测试过程名称: 多路CAN信息汇总
  测试用例名称: 缺失
  ---

--- 附加检查 ---
封面修订页格式正确
漏洞统计信息正确

处理文件夹: PK1B_L2
Word文件: 网络安全确认测试报告_【PK1B L2】_【整车】_【20250912】(1).docx
Excel文件: 网络安全确认测试用例_【PK1B L2】_【整车】_【20250912】.xlsx
测试报告汇总数据量: 80
测试过程数据量: 80
测试用例数据量: 80

=== 处理文件夹: PK1B_L2 ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
漏洞统计检查问题:
  - 安全等级不匹配: 报告显示S0级，根据漏洞统计应为S2级
  - 漏洞统计: 超高危0个，高危0个，中危1个，低危0个

处理文件夹: PK1B_L4
Word文件: 网络安全确认测试报告_【PK1B L4】_【整车】_【20250912】.docx
Excel文件: 网络安全确认测试用例_【PK1B L4】_【整车】_【20250912】.xlsx
测试报告汇总数据量: 44
测试过程数据量: 44
测试用例数据量: 44

=== 处理文件夹: PK1B_L4 ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
漏洞统计信息正确

==================================================
总结报告
==================================================
发现不一致的文件夹:
  Fr_camera: 2 个不一致项
  PK1B_ADCU: 2 个不一致项

报告生成完成！
详细结果已保存到: 测试数据对比结果_20250919_172056.txt
