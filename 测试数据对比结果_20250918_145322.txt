测试数据对比结果报告
生成时间: 2025-09-18 14:53:22
处理文件夹数量: 7
文件夹列表: Fr_camera, LK1A_CDC, LK2A_CCM, LK2A_TCU, PK1B_ADCU, PK1B_L2, PK1B_L4
================================================================================


处理文件夹: Fr_camera
Word文件: 网络安全确认测试报告_【LK2A】_【Fr_camera】_【20250829】.docx
Excel文件: LK2A_Fr_camera测试用例.xlsx
测试报告汇总数据量: 48
测试过程数据量: 49
测试用例数据量: 48

=== 处理文件夹: Fr_camera ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN_005
  测试报告汇总名称: XCP协议扫描测试-CAN5
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN_005
  测试报告汇总名称: XCP协议扫描测试-CAN5
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: /
  测试报告汇总名称: 缺失
  测试过程名称: 多路CAN信息汇总
  ---
  对比: 测试过程 vs 测试用例
  用例编号: /
  测试过程名称: 多路CAN信息汇总
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN_004
  测试报告汇总名称: CAN总线模糊测试-CAN5
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN_004
  测试报告汇总名称: CAN总线模糊测试-CAN5
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN_001
  测试报告汇总名称: CAN总线逆向测试-CAN5
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN_001
  测试报告汇总名称: CAN总线逆向测试-CAN5
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN_002
  测试报告汇总名称: CAN总线重放测试-CAN5
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN_002
  测试报告汇总名称: CAN总线重放测试-CAN5
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN5
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN5
  测试用例名称: CAN总线拒绝服务攻击
  ---

处理文件夹: LK1A_CDC
Word文件: LK1A_CDC_8295渗透测试报告V2.1.1.docx
Excel文件: LK1A_CDC_8295测试用例.xlsx
测试报告汇总数据量: 82
测试过程数据量: 81
测试用例数据量: 82

=== 处理文件夹: LK1A_CDC ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-BT-009
  测试报告汇总名称: 蓝牙通信连接设备占用测试
  测试过程名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT-INCUS-BT-009
  测试过程名称: 缺失
  测试用例名称: 蓝牙通信连接设备占用测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-DS-002
  测试报告汇总名称: 敏感数据存储安全
  测试过程名称: 敏感数据存储测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-DS-002
  测试报告汇总名称: 敏感数据存储安全
  测试用例名称: 敏感数据存储测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-WIFI-007
  测试报告汇总名称: WiFi模糊测试
  测试过程名称: WiFi密码枚举测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-WIFI-007
  测试报告汇总名称: WiFi模糊测试
  测试用例名称: WiFi密码枚举测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-WIFI-006
  测试报告汇总名称: WiFi密码枚举测试
  测试过程名称: WiFi模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-WIFI-006
  测试报告汇总名称: WiFi密码枚举测试
  测试用例名称: WiFi模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT-INCUS-ETH-001
  测试报告汇总名称: 以太网拒绝服务测试
  测试过程名称: 以太网拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT-INCUS-ETH-001
  测试报告汇总名称: 以太网拒绝服务测试
  测试用例名称: 以太网拒绝服务攻击
  ---

处理文件夹: LK2A_CCM
Word文件: 010-5 网络安全确认测试报告_【LK2A】_【CCM】_【20250822】.docx
Excel文件: LK2A_CCM测试用例.xlsx
测试报告汇总数据量: 57
测试过程数据量: 57
测试用例数据量: 57

=== 处理文件夹: LK2A_CCM ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_008
  测试报告汇总名称: 27服务安全
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_008
  测试报告汇总名称: 27服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_006
  测试报告汇总名称: 缺失
  测试过程名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_006
  测试报告汇总名称: 缺失
  测试用例名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_003
  测试报告汇总名称: 缺失
  测试过程名称: 种子随机度安全测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_003
  测试报告汇总名称: 缺失
  测试用例名称: 种子随机度安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_005
  测试报告汇总名称: 读写服务安全
  测试过程名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_005
  测试报告汇总名称: 读写服务安全
  测试用例名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_004
  测试报告汇总名称: 种子随机度安全测试
  测试过程名称: 读写服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_004
  测试报告汇总名称: 种子随机度安全测试
  测试用例名称: 读写服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_009
  测试报告汇总名称: 31服务安全
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_009
  测试报告汇总名称: 31服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_007
  测试报告汇总名称: 2F服务安全
  测试过程名称: 31服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_007
  测试报告汇总名称: 2F服务安全
  测试用例名称: 31服务安全
  ---

处理文件夹: LK2A_TCU
Word文件: 010-5 网络安全确认测试报告_【LK2A】_【TCU】_【20250904】.docx
Excel文件: LK2A_TCU测试用例.xlsx
测试报告汇总数据量: 62
测试过程数据量: 62
测试用例数据量: 62

=== 处理文件夹: LK2A_TCU ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_HW_001
  测试报告汇总名称: 丝印信息泄露-低配
  测试过程名称: 丝印信息泄露
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_HW_001
  测试报告汇总名称: 丝印信息泄露-低配
  测试用例名称: 丝印信息泄露
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_BLE_001
  测试报告汇总名称: BLE 从机连接占用测试-高配
  测试过程名称: BLE 从机连接占用测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_BLE_001
  测试报告汇总名称: BLE 从机连接占用测试-高配
  测试用例名称: BLE 从机连接占用测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_BLE_003
  测试报告汇总名称: 蓝牙漏洞测试-高配
  测试过程名称: 蓝牙漏洞测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_BLE_003
  测试报告汇总名称: 蓝牙漏洞测试-高配
  测试用例名称: 蓝牙漏洞测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_HW_005
  测试报告汇总名称: emmc芯片固件提取-低配
  测试过程名称: emmc芯片固件提取
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_HW_005
  测试报告汇总名称: emmc芯片固件提取-低配
  测试用例名称: emmc芯片固件提取
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_HW_004
  测试报告汇总名称: 调试接口固件提取测试-低配
  测试过程名称: 调试接口固件提取测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_HW_004
  测试报告汇总名称: 调试接口固件提取测试-低配
  测试用例名称: 调试接口固件提取测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_BLE_002
  测试报告汇总名称: BLE pin 码嗅探测试-高配
  测试过程名称: BLE pin 码嗅探测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_BLE_002
  测试报告汇总名称: BLE pin 码嗅探测试-高配
  测试用例名称: BLE pin 码嗅探测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_HW_003
  测试报告汇总名称: 调试接口安全防护测试-低配
  测试过程名称: 调试接口安全防护测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_HW_003
  测试报告汇总名称: 调试接口安全防护测试-低配
  测试用例名称: 调试接口安全防护测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_HW_002
  测试报告汇总名称: 调试接口暴露测试-低配
  测试过程名称: 调试接口暴露测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_HW_002
  测试报告汇总名称: 调试接口暴露测试-低配
  测试用例名称: 调试接口暴露测试
  ---

处理文件夹: PK1B_ADCU
Word文件: 网络安全确认测试报告_【PK1B】_【ADCU】_【20250829】.docx
Excel文件: PK1B_ADCU测试用例.xlsx
测试报告汇总数据量: 93
测试过程数据量: 102
测试用例数据量: 101

=== 处理文件夹: PK1B_ADCU ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN5_001
  测试报告汇总名称: CAN总线逆向测试-CAN5
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN5_001
  测试报告汇总名称: CAN总线逆向测试-CAN5
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN5_001
  测试过程名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN4_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN4_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN4_001
  测试报告汇总名称: CAN总线逆向测试-CAN4
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN4_001
  测试报告汇总名称: CAN总线逆向测试-CAN4
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN4_001
  测试过程名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN8_001
  测试报告汇总名称: CAN总线逆向测试-CAN8
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN8_001
  测试报告汇总名称: CAN总线逆向测试-CAN8
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN8_001
  测试过程名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN3_002
  测试报告汇总名称: CAN总线重放测试-CAN3
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN3_002
  测试报告汇总名称: CAN总线重放测试-CAN3
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN3_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN2_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN2_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_USB_001
  测试报告汇总名称: 缺失
  测试过程名称: 离线包完整性测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_USB_001
  测试报告汇总名称: 缺失
  测试用例名称: 离线包完整性测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_002_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 诊断复位测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_002_CAN
  测试过程名称: 缺失
  测试用例名称: 诊断复位测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN8_004
  测试报告汇总名称: CAN总线模糊测试-CAN8
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN8_004
  测试报告汇总名称: CAN总线模糊测试-CAN8
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN8_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN2_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN2
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN2_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN2
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN2_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_008
  测试报告汇总名称: 缺失
  测试用例名称: 31服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_008
  测试过程名称: 缺失
  测试用例名称: 31服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN7_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN7_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_006
  测试报告汇总名称: 缺失
  测试用例名称: 2F服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_006
  测试过程名称: 缺失
  测试用例名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN2_002
  测试报告汇总名称: CAN总线重放测试-CAN2
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN2_002
  测试报告汇总名称: CAN总线重放测试-CAN2
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN2_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_002 _DoIP
  测试报告汇总名称: 诊断复位测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_002 _DoIP
  测试过程名称: 诊断复位测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN3_001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN3_001
  测试过程名称: CAN总线逆向测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN7_001
  测试报告汇总名称: CAN总线逆向测试-CAN7
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN7_001
  测试报告汇总名称: CAN总线逆向测试-CAN7
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN7_001
  测试过程名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_003
  测试报告汇总名称: 缺失
  测试用例名称: 种子随机度安全测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_003
  测试过程名称: 缺失
  测试用例名称: 种子随机度安全测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_001 _DoIP
  测试报告汇总名称: 研发生产调试命令去除测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_001 _DoIP
  测试过程名称: 研发生产调试命令去除测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN5_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN5
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN5_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN5
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN5_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN6_001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN6_001
  测试过程名称: CAN总线逆向测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN8_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN8_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_004 _DoIP
  测试报告汇总名称: 读写服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_004 _DoIP
  测试过程名称: 读写服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_006 _CAN
  测试报告汇总名称: 27服务安全
  测试过程名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_006 _CAN
  测试报告汇总名称: 27服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_006 _CAN
  测试过程名称: 2F服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_USB_003
  测试报告汇总名称: 缺失
  测试过程名称: USB接口功能分析
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_USB_003
  测试报告汇总名称: 缺失
  测试用例名称: USB接口功能分析
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN8_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN8_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN7_002
  测试报告汇总名称: CAN总线重放测试-CAN7
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN7_002
  测试报告汇总名称: CAN总线重放测试-CAN7
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN7_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ETH_005
  测试报告汇总名称: SOME/IP模糊测试
  测试过程名称: 以太网设备传输安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_ETH_005
  测试报告汇总名称: SOME/IP模糊测试
  测试用例名称: 以太网设备传输安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_008 _CAN
  测试报告汇总名称: 缺失
  测试过程名称: 31服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_008 _CAN
  测试过程名称: 31服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN8_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN8_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN5_001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN5_001
  测试过程名称: CAN总线逆向测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN3_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN3_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_005
  测试报告汇总名称: 缺失
  测试用例名称: 23服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_005
  测试过程名称: 缺失
  测试用例名称: 23服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN2_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN2_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN8_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN8_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_USB_004
  测试报告汇总名称: 缺失
  测试过程名称: USB文件模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_USB_004
  测试报告汇总名称: 缺失
  测试用例名称: USB文件模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN6_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN6_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_004_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 读写服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_004_CAN
  测试过程名称: 缺失
  测试用例名称: 读写服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_001
  测试报告汇总名称: 缺失
  测试用例名称: 研发生产调试命令去除测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_001
  测试过程名称: 缺失
  测试用例名称: 研发生产调试命令去除测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN5_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN5_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_008 _DoIP
  测试报告汇总名称: 缺失
  测试过程名称: 31服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_008 _DoIP
  测试过程名称: 31服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_002 _CAN
  测试报告汇总名称: 诊断复位测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_002 _CAN
  测试过程名称: 诊断复位测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN3_005
  测试报告汇总名称: XCP协议扫描测试-CAN3
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN3_005
  测试报告汇总名称: XCP协议扫描测试-CAN3
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN3_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_007 _DoIP
  测试报告汇总名称: 31服务安全
  测试过程名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_007 _DoIP
  测试报告汇总名称: 31服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_007 _DoIP
  测试过程名称: 27服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN4_001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN4_001
  测试过程名称: CAN总线逆向测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_001_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 研发生产调试命令去除测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_001_CAN
  测试过程名称: 缺失
  测试用例名称: 研发生产调试命令去除测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_005 _DoIP
  测试报告汇总名称: 2F服务安全
  测试过程名称: 23服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_005 _DoIP
  测试报告汇总名称: 2F服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_005 _DoIP
  测试过程名称: 23服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_001 _CAN
  测试报告汇总名称: 研发生产调试命令去除测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_001 _CAN
  测试过程名称: 研发生产调试命令去除测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_006 _DoIP
  测试报告汇总名称: 27服务安全
  测试过程名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_006 _DoIP
  测试报告汇总名称: 27服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_006 _DoIP
  测试过程名称: 2F服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN7_005
  测试报告汇总名称: XCP协议扫描测试-CAN7
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN7_005
  测试报告汇总名称: XCP协议扫描测试-CAN7
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN7_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN5_005
  测试报告汇总名称: XCP协议扫描测试-CAN5
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN5_005
  测试报告汇总名称: XCP协议扫描测试-CAN5
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN5_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN4_005
  测试报告汇总名称: XCP协议扫描测试-CAN4
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN4_005
  测试报告汇总名称: XCP协议扫描测试-CAN4
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN4_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN3_004
  测试报告汇总名称: CAN总线模糊测试-CAN3
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN3_004
  测试报告汇总名称: CAN总线模糊测试-CAN3
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN3_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_USB_002
  测试报告汇总名称: 缺失
  测试过程名称: 离线包真实性测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_USB_002
  测试报告汇总名称: 缺失
  测试用例名称: 离线包真实性测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN2_001
  测试报告汇总名称: CAN总线逆向测试-CAN2
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN2_001
  测试报告汇总名称: CAN总线逆向测试-CAN2
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_008_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 31服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_008_CAN
  测试过程名称: 缺失
  测试用例名称: 31服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN5_004
  测试报告汇总名称: CAN总线模糊测试-CAN5
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN5_004
  测试报告汇总名称: CAN总线模糊测试-CAN5
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN5_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN6_004
  测试报告汇总名称: CAN总线模糊测试-CAN6
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN6_004
  测试报告汇总名称: CAN总线模糊测试-CAN6
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN6_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN7_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN7_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN5_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN5_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: /
  测试报告汇总名称: 缺失
  测试过程名称: 多路CAN信息汇总
  ---
  对比: 测试过程 vs 测试用例
  用例编号: /
  测试过程名称: 多路CAN信息汇总
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_002
  测试报告汇总名称: 缺失
  测试用例名称: 诊断复位测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_002
  测试过程名称: 缺失
  测试用例名称: 诊断复位测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_005_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 23服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_005_CAN
  测试过程名称: 缺失
  测试用例名称: 23服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN7_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN7
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN7_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN7
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN7_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ETH_004
  测试报告汇总名称: DoIP模糊测试
  测试过程名称: SOME/IP模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_ETH_004
  测试报告汇总名称: DoIP模糊测试
  测试用例名称: SOME/IP模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_003 _DoIP
  测试报告汇总名称: 种子随机度安全测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_003 _DoIP
  测试过程名称: 种子随机度安全测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN6_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN6_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN6_001
  测试报告汇总名称: CAN总线逆向测试-CAN6
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN6_001
  测试报告汇总名称: CAN总线逆向测试-CAN6
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN6_001
  测试过程名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN4_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN4_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN3_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN3_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_USB_006
  测试报告汇总名称: 缺失
  测试过程名称: 恶意木马导入
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_USB_006
  测试报告汇总名称: 缺失
  测试用例名称: 恶意木马导入
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN7_004
  测试报告汇总名称: CAN总线模糊测试-CAN7
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN7_004
  测试报告汇总名称: CAN总线模糊测试-CAN7
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN7_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_USB_005
  测试报告汇总名称: 缺失
  测试过程名称: 固件更新逻辑分析
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_USB_005
  测试报告汇总名称: 缺失
  测试用例名称: 固件更新逻辑分析
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN3_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN3_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN7_001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN7_001
  测试过程名称: CAN总线逆向测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_003_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 种子随机度安全测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_003_CAN
  测试过程名称: 缺失
  测试用例名称: 种子随机度安全测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN8_002
  测试报告汇总名称: CAN总线重放测试-CAN8
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN8_002
  测试报告汇总名称: CAN总线重放测试-CAN8
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN8_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN3_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN3_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ETH_002
  测试报告汇总名称: 以太网端口扫描
  测试过程名称: 以太网危险端口测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_ETH_002
  测试报告汇总名称: 以太网端口扫描
  测试用例名称: 以太网危险端口测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN5_002
  测试报告汇总名称: CAN总线重放测试-CAN5
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN5_002
  测试报告汇总名称: CAN总线重放测试-CAN5
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN5_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN8_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN8
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN8_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN8
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN8_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_007 _CAN
  测试报告汇总名称: 31服务安全
  测试过程名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_007 _CAN
  测试报告汇总名称: 31服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_007 _CAN
  测试过程名称: 27服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN3_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN3
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN3_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN3
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN3_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_007_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 27服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_007_CAN
  测试过程名称: 缺失
  测试用例名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ETH_003
  测试报告汇总名称: 以太网危险端口测试
  测试过程名称: DoIP模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_ETH_003
  测试报告汇总名称: 以太网危险端口测试
  测试用例名称: DoIP模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN2_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN2_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN8_005
  测试报告汇总名称: XCP协议扫描测试-CAN8
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN8_005
  测试报告汇总名称: XCP协议扫描测试-CAN8
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN8_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN4_002
  测试报告汇总名称: CAN总线重放测试-CAN4
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN4_002
  测试报告汇总名称: CAN总线重放测试-CAN4
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN4_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN4_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN4
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN4_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN4
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN4_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN6_002
  测试报告汇总名称: CAN总线重放测试-CAN6
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN6_002
  测试报告汇总名称: CAN总线重放测试-CAN6
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN6_002
  测试过程名称: 缺失
  测试用例名称: CAN总线重放测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_003 _CAN
  测试报告汇总名称: 种子随机度安全测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_003 _CAN
  测试过程名称: 种子随机度安全测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN6_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN6_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_006_CAN
  测试报告汇总名称: 缺失
  测试用例名称: 2F服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_006_CAN
  测试过程名称: 缺失
  测试用例名称: 2F服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_UDS_005 _CAN
  测试报告汇总名称: 2F服务安全
  测试过程名称: 23服务安全
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_005 _CAN
  测试报告汇总名称: 2F服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_005 _CAN
  测试过程名称: 23服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_004 _CAN
  测试报告汇总名称: 读写服务安全
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_004 _CAN
  测试过程名称: 读写服务安全
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN7_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN7_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN4_004
  测试报告汇总名称: CAN总线模糊测试-CAN4
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN4_004
  测试报告汇总名称: CAN总线模糊测试-CAN4
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN4_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN2_002
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线重放测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN2_002
  测试过程名称: CAN总线重放测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN4_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN4_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_004
  测试报告汇总名称: 缺失
  测试用例名称: 读写服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_004
  测试过程名称: 缺失
  测试用例名称: 读写服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN5_003
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN5_003
  测试过程名称: CAN总线拒绝服务攻击
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN2_005
  测试报告汇总名称: XCP协议扫描测试-CAN2
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN2_005
  测试报告汇总名称: XCP协议扫描测试-CAN2
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN2_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN6_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN6
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN6_003
  测试报告汇总名称: CAN总线拒绝服务攻击-CAN6
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN6_003
  测试过程名称: 缺失
  测试用例名称: CAN总线拒绝服务攻击
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN7_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN7_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN6_005
  测试报告汇总名称: XCP协议扫描测试-CAN6
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN6_005
  测试报告汇总名称: XCP协议扫描测试-CAN6
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN6_005
  测试过程名称: 缺失
  测试用例名称: XCP协议扫描测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN5_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN5_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN8_001
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN8_001
  测试过程名称: CAN总线逆向测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN2_004
  测试报告汇总名称: CAN总线模糊测试-CAN2
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN2_004
  测试报告汇总名称: CAN总线模糊测试-CAN2
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN2_004
  测试过程名称: 缺失
  测试用例名称: CAN总线模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_UDS_007
  测试报告汇总名称: 缺失
  测试用例名称: 27服务安全
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_UDS_007
  测试过程名称: 缺失
  测试用例名称: 27服务安全
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN3_001
  测试报告汇总名称: CAN总线逆向测试-CAN3
  测试过程名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN3_001
  测试报告汇总名称: CAN总线逆向测试-CAN3
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_CAN3_001
  测试过程名称: 缺失
  测试用例名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN4_004
  测试报告汇总名称: 缺失
  测试过程名称: CAN总线模糊测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN4_004
  测试过程名称: CAN总线模糊测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_ CAN6_005
  测试报告汇总名称: 缺失
  测试过程名称: XCP协议扫描测试
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_ CAN6_005
  测试过程名称: XCP协议扫描测试
  测试用例名称: 缺失
  ---

处理文件夹: PK1B_L2
Word文件: 网络安全确认测试报告_【PK1B L2】_【整车】_【20250912】.docx
Excel文件: PK1B L2整车测试用例.xlsx
测试报告汇总数据量: 80
测试过程数据量: 80
测试用例数据量: 80

=== 处理文件夹: PK1B_L2 ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_BTK_006
  测试报告汇总名称: 多模蓝牙钥匙模糊测试
  测试过程名称: 蓝牙钥匙模糊测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_BTK_006
  测试报告汇总名称: 多模蓝牙钥匙模糊测试
  测试用例名称: 蓝牙钥匙模糊测试
  ---

处理文件夹: PK1B_L4
Word文件: 网络安全确认测试报告_【PK1B L4】_【整车】_【20250904】.docx
Excel文件: PK1B L4整车测试用例.xlsx
测试报告汇总数据量: 44
测试过程数据量: 44
测试用例数据量: 44

=== 处理文件夹: PK1B_L4 ===
  所有数据一致！

==================================================
总结报告
==================================================
发现不一致的文件夹:
  Fr_camera: 12 个不一致项
  LK1A_CDC: 10 个不一致项
  LK2A_CCM: 14 个不一致项
  LK2A_TCU: 16 个不一致项
  PK1B_ADCU: 264 个不一致项
  PK1B_L2: 2 个不一致项

报告生成完成！
详细结果已保存到: 测试数据对比结果_20250918_145322.txt
