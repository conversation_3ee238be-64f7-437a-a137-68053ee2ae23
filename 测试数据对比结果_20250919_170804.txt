测试数据对比结果报告
生成时间: 2025-09-19 17:08:04
处理文件夹数量: 7
文件夹列表: Fr_camera, LK1A_CDC, LK2A_CCM, LK2A_TCU, PK1B_ADCU, PK1B_L2, PK1B_L4
================================================================================


处理文件夹: Fr_camera
Word文件: 网络安全确认测试报告_【LK2A】_【Fr_camera】_【20250829】.docx
Excel文件: 网络安全确认测试用例_【LK2A】_【Fr_camera】_【20250829】.xlsx
测试报告汇总数据量: 58
测试过程数据量: 59
测试用例数据量: 58

=== 处理文件夹: Fr_camera ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: /
  测试报告汇总名称: 缺失
  测试过程名称: 多路CAN信息汇总
  ---
  对比: 测试过程 vs 测试用例
  用例编号: /
  测试过程名称: 多路CAN信息汇总
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_CAN1_001
  测试报告汇总名称: CAN总线逆向测
  测试过程名称: CAN总线逆向测试
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_CAN1_001
  测试报告汇总名称: CAN总线逆向测
  测试用例名称: CAN总线逆向测试
  ---

--- 附加检查 ---
封面修订页格式正确
漏洞统计检查问题:
  - 未找到漏洞统计数据

处理文件夹: LK1A_CDC
Word文件: LK1A_CDC_8295渗透测试报告V2.1.1.docx
Excel文件: LK1A_CDC_8295测试用例.xlsx
测试报告汇总数据量: 96
测试过程数据量: 96
测试用例数据量: 96

=== 处理文件夹: LK1A_CDC ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式问题:
  - 第2行版本号格式错误: '型号规格' (应为Vx.x格式)
  - 第2行日期格式错误: '版本' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第3行版本号格式错误: '测试时间' (应为Vx.x格式)
  - 第3行日期格式错误: '2024年10月29日-2025年3月14日' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第4行版本号格式错误: '测试结论' (应为Vx.x格式)
  - 第4行日期格式错误: 'S0(-)级  S1(A)级   S2(B)级  S3(C)级  S4(D)级' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第5行版本号格式错误: '结论描述' (应为Vx.x格式)
  - 第5行日期格式错误: '受测系统在第一轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0个，高危漏洞 6 个，中危漏洞 5 个，低危漏洞 9 个。
受测系统在第二轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0个，高危漏洞 0 个，中危漏洞 3 个，低危漏洞 2 个。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S2（B）级，中安全隐患系统 。' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第6行版本号格式错误: '测试人' (应为Vx.x格式)
  - 第6行日期格式错误: '日期' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第7行版本号格式错误: '审核人' (应为Vx.x格式)
  - 第7行日期格式错误: '日期' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第8行版本号格式错误: '批准人' (应为Vx.x格式)
  - 第8行日期格式错误: '日期' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第9行版本号格式错误: '成果密级' (应为Vx.x格式)
  - 第9行日期格式错误: '3级（可公开）    2级（内部传阅）    1级（限制传阅）' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第10行版本号格式错误: '送测单位' (应为Vx.x格式)
  - 第10行日期格式错误: '测试单位' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第11行版本号格式错误: '单位地址' (应为Vx.x格式)
  - 第11行日期格式错误: '单位地址' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第12行版本号格式错误: '联系人' (应为Vx.x格式)
  - 第12行日期格式错误: '联系人' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第13行版本号格式错误: '联系电话' (应为Vx.x格式)
  - 第13行日期格式错误: '联系电话' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第14行版本号格式错误: '电子邮箱' (应为Vx.x格式)
  - 第14行日期格式错误: '电子邮箱' (应为xxxx/xx/xx-xxxx/xx/xx格式)
漏洞统计信息正确

处理文件夹: LK2A_CCM
Word文件: 010-5 网络安全确认测试报告_【LK2A】_【CCM】_【20250822】.docx
Excel文件: 010-5 网络安全确认测试用例_【LK2A】_【CCM】_【20250822】.xlsx
测试报告汇总数据量: 57
测试过程数据量: 57
测试用例数据量: 57

=== 处理文件夹: LK2A_CCM ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
漏洞统计检查问题:
  - 未找到漏洞统计数据

处理文件夹: LK2A_TCU
Word文件: 网络安全确认测试报告_【LK2A】_【TCU】_【20250904】.docx
Excel文件: 网络安全确认测试用例_【LK2A】_【TCU】_【20250904】.xlsx
测试报告汇总数据量: 72
测试过程数据量: 71
测试用例数据量: 70

=== 处理文件夹: LK2A_TCU ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_SYS_009
  测试报告汇总名称: 系统漏洞扫描
  测试过程名称: 工程模式安全防护
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_SYS_009
  测试过程名称: 工程模式安全防护
  测试用例名称: 系统漏洞扫描
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_BLE_002_2JG0B
  测试报告汇总名称: BLE pin 码嗅探测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_BLE_002_2JG0B
  测试过程名称: BLE pin 码嗅探测试
  测试用例名称: 缺失
  ---
  对比: 测试报告汇总 vs 测试过程
  用例编号: PT_SYS_010
  测试报告汇总名称: 工程模式安全防护
  测试过程名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_SYS_010
  测试过程名称: 缺失
  测试用例名称: 工程模式安全防护
  ---
  对比: 测试报告汇总 vs 测试用例
  用例编号: PT_BLE_003_2JG0B
  测试报告汇总名称: 蓝牙漏洞测试
  测试用例名称: 缺失
  ---
  对比: 测试过程 vs 测试用例
  用例编号: PT_BLE_003_2JG0B
  测试过程名称: 蓝牙漏洞测试
  测试用例名称: 缺失
  ---

--- 附加检查 ---
封面修订页格式正确
漏洞统计检查问题:
  - 未找到漏洞统计数据

处理文件夹: PK1B_ADCU
Word文件: 网络安全确认测试报告_【PK1B】_【ADCU】_【20250829】.docx
Excel文件: 网络安全确认测试用例_【PK1B】_【ADCU】_【20250829】.xlsx
测试报告汇总数据量: 101
测试过程数据量: 102
测试用例数据量: 101

=== 处理文件夹: PK1B_ADCU ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: /
  测试报告汇总名称: 缺失
  测试过程名称: 多路CAN信息汇总
  ---
  对比: 测试过程 vs 测试用例
  用例编号: /
  测试过程名称: 多路CAN信息汇总
  测试用例名称: 缺失
  ---

--- 附加检查 ---
封面修订页格式正确
漏洞统计检查问题:
  - 未找到漏洞统计数据

处理文件夹: PK1B_L2
Word文件: 网络安全确认测试报告_【PK1B L2】_【整车】_【20250912】(1).docx
Excel文件: 网络安全确认测试用例_【PK1B L2】_【整车】_【20250912】.xlsx
测试报告汇总数据量: 80
测试过程数据量: 80
测试用例数据量: 80

=== 处理文件夹: PK1B_L2 ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式问题:
  - 第2行日期格式错误: '2025-06-19-2025-07-24' (应为xxxx/xx/xx-xxxx/xx/xx格式)
  - 第3行日期格式错误: '2025-07-28-2025-07-29' (应为xxxx/xx/xx-xxxx/xx/xx格式)
漏洞统计检查问题:
  - 未找到漏洞统计数据

处理文件夹: PK1B_L4
Word文件: 网络安全确认测试报告_【PK1B L4】_【整车】_【20250912】.docx
Excel文件: 网络安全确认测试用例_【PK1B L4】_【整车】_【20250912】.xlsx
测试报告汇总数据量: 44
测试过程数据量: 44
测试用例数据量: 44

=== 处理文件夹: PK1B_L4 ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
漏洞统计检查问题:
  - 未找到漏洞统计数据

==================================================
总结报告
==================================================
发现不一致的文件夹:
  Fr_camera: 4 个不一致项
  LK2A_TCU: 8 个不一致项
  PK1B_ADCU: 2 个不一致项

报告生成完成！
详细结果已保存到: 测试数据对比结果_20250919_170804.txt
